import pandas as pd
import numpy as np
from talib import abstract

class TAWrapper:
    def __init__(self, df: pd.DataFrame):
        """
        df debe tener columnas: 'open', 'high', 'low', 'close', 'volume'
        """
        self.df = df

    def __getattr__(self, name):
        """
        Cualquier atributo desconocido lo tratamos como función de TA-Lib.
        Ej: ta.SMA(20) → busca 'sma' en TA-Lib.
        """
        try:
            func = abstract.Function(name.lower())
        except Exception as e:
            raise AttributeError(f"{name} no es un indicador válido de TA-Lib") from e

        # Retornamos un callable que acepta kwargs y usa nuestro DataFrame
        def wrapper(**kwargs):
            return func(self.df, **kwargs)

        return wrapper
