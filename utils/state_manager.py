import json
import os
import logging

logger = logging.getLogger()


class StateManager:
    """Handles loading and saving the bot's state to a JSON file."""

    def __init__(self, state_file="bot_state.json"):
        self.state_file = state_file
        self.state = self._load_state()

    def _load_state(self):
        """Loads the state from the JSON file if it exists."""
        if os.path.exists(self.state_file):
            try:
                with open(self.state_file, "r") as f:
                    logger.info(f"Loading state from {self.state_file}")
                    return json.load(f)
            except (json.JSONDecodeError, IOError) as e:
                logger.error(
                    f"Error loading state file: {e}. Starting with a fresh state."
                )
                return self._get_initial_state()
        return self._get_initial_state()

    def _get_initial_state(self):
        """Defines the default structure of the state."""
        return {
            "open_position": None,  # Holds data for the current active trade
            "trade_history": [],
        }

    def save_state(self):
        """Saves the current state to the JSON file."""
        try:
            with open(self.state_file, "w") as f:
                json.dump(self.state, f, indent=4)
            # logger.info(f"Successfully saved state to {self.state_file}") # Commented out to reduce log spam
        except IOError as e:
            logger.error(f"Could not save state to file: {e}")

    def get_position(self):
        """Returns the current open position."""
        return self.state.get("open_position")

    def set_position(self, position_data):
        """Updates the current open position and saves the state."""
        self.state["open_position"] = position_data
        self.save_state()

    def clear_position(self, closed_position):
        """Moves the closed position to history and clears the active one."""
        self.state["trade_history"].append(closed_position)
        self.state["open_position"] = None
        self.save_state()
