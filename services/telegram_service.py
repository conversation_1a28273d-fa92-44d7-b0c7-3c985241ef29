"""
Telegram Service for sending trading notifications.
Handles all Telegram bot communications for position updates.
"""

import time
from datetime import datetime
from typing import Optional, Dict, Any
from telegram import Update
from telegram.ext import Application, CommandHandler, CallbackContext
from utils.logger_setup import setup_logger
import asyncio
from telegram.constants import ParseMode
import threading


class TelegramService:
    """Service for sending Telegram notifications about trading activities."""

    def __init__(self, bot_token: str, chat_id: str):
        self.bot_token = bot_token
        self.chat_id = chat_id
        self.app = Application.builder().token(self.bot_token).build()
        self.logger = setup_logger()

    def _run_async_send(self, text: str):
        """Función interna para ser ejecutada en un hilo."""
        try:
            # Crea un nuevo loop de eventos de asyncio para este hilo
            # y ejecuta la corutina de envío de mensaje.
            asyncio.run(
                self.app.bot.send_message(
                    chat_id=self.chat_id, text=text, parse_mode="HTML"
                )
            )
            self.logger.info("Successfully sent Telegram notification.")
        except Exception as e:
            self.logger.error(f"Failed to send Telegram notification: {e}")

    def send_message(self, text: str):
        """
        Envía un mensaje en un hilo separado para no bloquear
        el programa principal síncrono.
        """
        if not self.app:
            return

        # Crea un hilo que ejecutará la función asíncrona
        thread = threading.Thread(target=self._run_async_send, args=(text,))
        thread.start()  # Inicia el hilo

    def send_position_opened(
        self, position: Dict[str, Any], ai_analysis: Optional[Dict] = None
    ) -> bool:
        """Send notification when a new position is opened."""
        try:
            # Calculate position value
            position_value = position["total_base_quantity"] * position["average_price"]

            # Format message
            message = f"🟢 <b>NEW POSITION OPENED</b>\n\n"
            message += f"💰 <b>Amount:</b> ${position['total_quote_spent']:.2f} USDT\n"
            message += (
                f"📊 <b>Quantity:</b> {position['total_base_quantity']:.8f} ETH\n"
            )
            message += f"💵 <b>Entry Price:</b> ${position['average_price']:.2f}\n"
            message += f"🎯 <b>Take Profit:</b> ${position['take_profit_price']:.2f}\n"

            # Calculate profit target
            profit_target = position["take_profit_price"] - position["average_price"]
            profit_pct = (profit_target / position["average_price"]) * 100
            message += (
                f"📈 <b>Profit Target:</b> ${profit_target:.2f} ({profit_pct:.1f}%)\n"
            )

            # Add AI analysis if available
            if ai_analysis:
                confidence = ai_analysis.get("confidence_score", 0)
                sentiment = ai_analysis.get("market_sentiment", "Unknown")
                message += f"\n🤖 <b>AI Analysis:</b>\n"
                message += f"   • Sentiment: {sentiment}\n"
                message += f"   • Confidence: {confidence:.1%}\n"

                if ai_analysis.get("reasoning"):
                    reasoning = (
                        ai_analysis["reasoning"][:100] + "..."
                        if len(ai_analysis["reasoning"]) > 100
                        else ai_analysis["reasoning"]
                    )
                    message += f"   • Reason: {reasoning}\n"

            # Add timestamp
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            message += f"\n⏰ <b>Time:</b> {timestamp}"

            return self.send_message(message)

        except Exception as e:
            self.logger.error(f"Error sending position opened notification: {e}")
            return False

    def send_position_closed(
        self, position: Dict[str, Any], exit_price: float, profit: float, reason: str
    ) -> bool:
        """Send notification when a position is closed."""
        try:
            profit_pct = (profit / position["total_quote_spent"]) * 100

            # Choose emoji based on profit/loss
            if profit > 0:
                status_emoji = "🟢"
                profit_emoji = "💰"
            elif profit < 0:
                status_emoji = "🔴"
                profit_emoji = "💸"
            else:
                status_emoji = "⚪"
                profit_emoji = "💱"

            # Format message
            message = f"{status_emoji} <b>POSITION CLOSED</b>\n\n"
            message += (
                f"📊 <b>Quantity:</b> {position['total_base_quantity']:.8f} ETH\n"
            )
            message += f"💵 <b>Entry Price:</b> ${position['average_price']:.2f}\n"
            message += f"💰 <b>Exit Price:</b> ${exit_price:.2f}\n"
            message += f"💸 <b>Total Spent:</b> ${position['total_quote_spent']:.2f}\n"

            # Profit/Loss section
            message += f"\n{profit_emoji} <b>RESULT:</b>\n"
            message += f"   • P&L: ${profit:.2f} ({profit_pct:+.2f}%)\n"
            message += f"   • Reason: {reason}\n"

            # Add holding time if available
            if position.get("last_ai_check_ts"):
                holding_time = time.time() - position["last_ai_check_ts"]
                hours = int(holding_time // 3600)
                minutes = int((holding_time % 3600) // 60)
                message += f"   • Holding Time: {hours}h {minutes}m\n"

            # Add timestamp
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            message += f"\n⏰ <b>Time:</b> {timestamp}"

            return self.send_message(message)

        except Exception as e:
            self.logger.error(f"Error sending position closed notification: {e}")
            return False

    def send_dca_order(
        self, position: Dict[str, Any], dca_order: Dict[str, Any]
    ) -> bool:
        """Send notification when a DCA order is executed."""
        try:
            dca_count = len(position.get("dca_orders", []))

            message = f"🔄 <b>DCA ORDER #{dca_count}</b>\n\n"
            message += f"💰 <b>Amount:</b> ${dca_order['quote_size']:.2f} USDT\n"
            message += f"📊 <b>Quantity:</b> {dca_order['base_quantity']:.8f} ETH\n"
            message += f"💵 <b>Price:</b> ${dca_order['price']:.2f}\n"

            # Updated position info
            message += f"\n📈 <b>UPDATED POSITION:</b>\n"
            message += (
                f"   • Total Quantity: {position['total_base_quantity']:.8f} ETH\n"
            )
            message += f"   • New Avg Price: ${position['average_price']:.2f}\n"
            message += f"   • Total Invested: ${position['total_quote_spent']:.2f}\n"
            message += f"   • New Take Profit: ${position['take_profit_price']:.2f}\n"

            # Add timestamp
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            message += f"\n⏰ <b>Time:</b> {timestamp}"

            return self.send_message(message)

        except Exception as e:
            self.logger.error(f"Error sending DCA order notification: {e}")
            return False

    def send_ai_alert(self, message: str, alert_type: str = "INFO") -> bool:
        """Send AI-generated alerts."""
        try:
            emoji_map = {"INFO": "ℹ️", "WARNING": "⚠️", "ERROR": "❌", "SUCCESS": "✅"}

            emoji = emoji_map.get(alert_type, "ℹ️")
            formatted_message = f"{emoji} <b>AI ALERT</b>\n\n{message}"

            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            formatted_message += f"\n\n⏰ {timestamp}"

            return self.send_message(formatted_message)

        except Exception as e:
            self.logger.error(f"Error sending AI alert: {e}")
            return False

    def send_system_status(self, status: str, details: Optional[str] = None) -> bool:
        """Send system status updates."""
        try:
            message = f"🤖 <b>SYSTEM STATUS</b>\n\n"
            message += f"Status: {status}\n"

            if details:
                message += f"Details: {details}\n"

            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            message += f"\n⏰ {timestamp}"

            return self.send_message(message)

        except Exception as e:
            self.logger.error(f"Error sending system status: {e}")
            return False
