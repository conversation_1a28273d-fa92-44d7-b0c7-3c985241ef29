from typing import override
import numpy as np
from utils.talib_wrapper import TA<PERSON>rapper
from services.binance_client import BinanceClient
import pandas as pd

# Simulamos un DataFrame OHLCV
df = pd.DataFrame({
    'open': np.random.random(100),
    'high': np.random.random(100) + 1,
    'low': np.random.random(100) - 1,
    'close': np.random.random(100),
    'volume': np.random.randint(100, 1000, 100)
})

class TAService:
    def __init__(self, binance_client: BinanceClient):
        self.binance_client = binance_client
        self.rsi = None
        self.atr = None
        self.ema = None
        self.sma = None
        self.sar = None
        self.bbands = {"upper": None, "middle": None, "lower": None}
        self.avg_price = None

    def get_indicatos_value(self):
        try:
            # Fetch historical data
            data = self.binance_client.get_historical_data()
            if data.empty:
                print("Data is empty")
                return None
            
            # Initialize TA-Lib wrapper
            ta = TAWrapper(data)
            # Calculate indicators
            self.sma = ta.SMA(timeperiod=200, price="close")
            self.ema = ta.EMA(timeperiod=200, price="close")
            self.rsi = ta.RSI(timeperiod=14, price="close")
            self.atr = ta.ATR(timeperiod=14, price="close")
            self.sar = ta.SAR(acceleration=0.02, maximum=0.2, price="close")

            self.bbands["upper"], self.bbands["middle"], self.bbands["lower"] = ta.BBANDS(data["close"],timeperiod=20)  
            print( self.bbands)
            self.avg_price = ta.AVGPRICE()


            return {
                "sma": self.sma,
                "ema": self.ema,
                "rsi": self.rsi,
                "atr": self.atr,
                "sar": self.sar,
                "bbands": self.bbands,
                "avg_price": self.avg_price,
            }
        
        except Exception as e:
            print(f"Error getting indicators: {e}")
            return None