#!/usr/bin/env python3
"""
Ejemplo de cómo usar el cliente asíncrono de Binance correctamente.
"""

import asyncio
import yaml
import sys
import os

# Agregar el directorio padre al path para importar módulos
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.binance_client import BinanceClient
from utils.logger_setup import setup_logger


def load_config(path="../config.yaml"):
    """Load configuration file."""
    with open(path, "r") as f:
        return yaml.safe_load(f)


async def example_async_operations():
    """Ejemplo de operaciones asíncronas con Binance."""
    print("🚀 Iniciando ejemplo de cliente asíncrono de Binance")

    # Cargar configuración
    config = load_config()
    logger = setup_logger("async_example.log")

    # Inicializar cliente
    binance_client = BinanceClient(config)

    try:
        print("\n1. 📊 Obteniendo precio de mercado (async)...")
        price_async = await binance_client.get_market_price_async()
        print(f"   Precio async: ${price_async:.2f}")

        # Comparar con versión síncrona
        price_sync = binance_client.get_market_price()
        print(f"   Precio sync:  ${price_sync:.2f}")

        print("\n2. 💰 Obteniendo balances (async)...")
        usdt_balance = await binance_client.get_account_balance_async("USDT")
        eth_balance = await binance_client.get_account_balance_async("ETH")

        if usdt_balance:
            print(
                f"   USDT Balance: {usdt_balance['free']} (free) + {usdt_balance['locked']} (locked)"
            )

        if eth_balance:
            print(
                f"   ETH Balance:  {eth_balance['free']} (free) + {eth_balance['locked']} (locked)"
            )

        print("\n3. 🔄 Ejemplo de orden asíncrona (SIMULACIÓN)...")
        print("   NOTA: Esta es solo una simulación, no se ejecutará orden real")

        # Simular orden pequeña
        test_amount = 5.0  # $5 USDT
        print(f"   Simulando orden de ${test_amount} USDT...")

        # En un caso real, descomenta la siguiente línea:
        # order_result = await binance_client.execute_market_order_quote_async('BUY', test_amount)
        # if order_result:
        #     print(f"   ✅ Orden ejecutada: {order_result.get('orderId')}")
        # else:
        #     print(f"   ❌ Orden falló")

        print("   ✅ Simulación completada")

        print("\n4. ⚡ Comparando velocidad async vs sync...")

        # Medir tiempo async
        import time

        start_time = time.time()
        tasks = [
            binance_client.get_market_price_async(),
            binance_client.get_account_balance_async("USDT"),
            binance_client.get_account_balance_async("ETH"),
        ]
        results = await asyncio.gather(*tasks)
        async_time = time.time() - start_time

        # Medir tiempo sync
        start_time = time.time()
        sync_price = binance_client.get_market_price()
        sync_usdt = binance_client.get_account_balance("USDT")
        sync_eth = binance_client.get_account_balance("ETH")
        sync_time = time.time() - start_time

        print(f"   Tiempo async: {async_time:.3f}s")
        print(f"   Tiempo sync:  {sync_time:.3f}s")
        print(f"   Mejora: {sync_time / async_time:.1f}x más rápido")

    except Exception as e:
        print(f"❌ Error en operaciones async: {e}")
        import traceback

        traceback.print_exc()

    finally:
        # Cerrar clientes correctamente
        print("\n5. 🔒 Cerrando conexiones...")
        binance_client.close_clients()
        print("   ✅ Conexiones cerradas")


def example_sync_operations():
    """Ejemplo de operaciones síncronas (método tradicional)."""
    print("\n" + "=" * 60)
    print("📊 EJEMPLO DE OPERACIONES SÍNCRONAS")
    print("=" * 60)

    config = load_config()
    binance_client = BinanceClient(config)

    try:
        print("\n1. Precio de mercado (sync)...")
        price = binance_client.get_market_price()
        print(f"   ${price:.2f}")

        print("\n2. Balances (sync)...")
        usdt_balance = binance_client.get_account_balance("USDT")
        if usdt_balance:
            print(f"   USDT: {usdt_balance['free']}")

        print("\n3. Información de símbolo...")
        # Ejemplo de método que solo existe en sync
        try:
            symbol_info = binance_client.client.get_symbol_info(
                binance_client.trading_pair
            )
            print(f"   Símbolo: {symbol_info['symbol']}")
            print(f"   Estado: {symbol_info['status']}")
        except Exception as e:
            print(f"   Error obteniendo info de símbolo: {e}")

    except Exception as e:
        print(f"❌ Error en operaciones sync: {e}")

    finally:
        binance_client.close_clients()


async def main():
    """Función principal que ejecuta todos los ejemplos."""
    print("🤖 EJEMPLOS DE CLIENTE BINANCE ASÍNCRONO")
    print("=" * 60)

    # Ejecutar ejemplos asíncronos
    await example_async_operations()

    # Ejecutar ejemplos síncronos
    example_sync_operations()

    print("\n" + "=" * 60)
    print("✅ EJEMPLOS COMPLETADOS")
    print("=" * 60)
    print("\n📝 RESUMEN:")
    print("• El cliente híbrido permite usar tanto métodos sync como async")
    print("• Los métodos async son más eficientes para múltiples operaciones")
    print("• Los métodos sync son más simples para operaciones individuales")
    print("• Siempre cierra las conexiones correctamente")
    print("\n🚀 ¡Tu cliente asíncrono está listo para usar!")


def run_sync_example():
    """Ejecutar solo ejemplos síncronos."""
    example_sync_operations()


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Ejemplos de cliente Binance")
    parser.add_argument(
        "--sync-only", action="store_true", help="Ejecutar solo ejemplos síncronos"
    )
    args = parser.parse_args()

    if args.sync_only:
        run_sync_example()
    else:
        # Ejecutar ejemplos asíncronos
        asyncio.run(main())
