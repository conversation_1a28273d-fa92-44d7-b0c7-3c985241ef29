from dataclasses import dataclass, field
from typing import Optional


@dataclass
class MarketData:
    rsi: Optional[float] = None
    atr: Optional[float] = None
    ema: Optional[float] = None
    current_price: Optional[float] = None
    news_headlines: Optional[str] = None
    sentiment_score: Optional[str] = None
    price_position: Optional[str] = None  # Above or below EMA
    ema_length: Optional[int] = None
    ema_timeframe: Optional[str] = None
    kline_interval: Optional[str] = None
    trading_pair: Optional[str] = None

    def is_trending_up(self) -> bool:
        """Un método simple para comprobar si el precio está por encima de la EMA."""
        if self.current_price is not None and self.ema is not None:
            return self.current_price > self.ema
        return False
