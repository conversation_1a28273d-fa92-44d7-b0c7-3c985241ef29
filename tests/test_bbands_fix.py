#!/usr/bin/env python3
"""
Test script to verify BBANDS fix in TAService
"""
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from services.ta_services import TAService
from services.binance_client import BinanceClient
from utils.load_config import load_config
import pandas as pd

def test_bbands_fix():
    """Test that BBANDS returns proper numerical values instead of column names"""
    print("🔧 Testing BBANDS fix...")
    
    config = load_config("config/config.yaml")
    binance_client = BinanceClient(config)
    ta_service = TAService(binance_client)
    
    # Get indicators
    indicators = ta_service.get_indicatos_value()
    
    if indicators is None:
        print("❌ Failed to get indicators")
        return False
    
    bbands = indicators.get('bbands')
    if bbands is None:
        print("❌ BBANDS not found in indicators")
        return False
    
    # Check that BBANDS contains the expected structure
    expected_keys = ['upper', 'middle', 'lower']
    for key in expected_keys:
        if key not in bbands:
            print(f"❌ Missing key '{key}' in BBANDS")
            return False
    
    # Check that values are pandas Series with numerical data (not strings)
    for key in expected_keys:
        value = bbands[key]
        if not isinstance(value, pd.Series):
            print(f"❌ BBANDS['{key}'] is not a pandas Series, got: {type(value)}")
            return False
        
        # Check if we have any non-NaN values (should have some after period 19)
        non_nan_values = value.dropna()
        if len(non_nan_values) == 0:
            print(f"⚠️  BBANDS['{key}'] has no non-NaN values (this might be expected with limited data)")
        else:
            # Check that non-NaN values are numerical
            if not pd.api.types.is_numeric_dtype(non_nan_values):
                print(f"❌ BBANDS['{key}'] contains non-numerical values")
                return False
            
            print(f"✅ BBANDS['{key}'] has {len(non_nan_values)} valid numerical values")
            print(f"   Latest value: {non_nan_values.iloc[-1]:.2f}")
    
    print("✅ BBANDS fix verified successfully!")
    return True

def test_other_indicators():
    """Test that other indicators are still working"""
    print("\n🔧 Testing other indicators...")
    
    config = load_config("config/config.yaml")
    binance_client = BinanceClient(config)
    ta_service = TAService(binance_client)
    
    indicators = ta_service.get_indicatos_value()
    
    if indicators is None:
        print("❌ Failed to get indicators")
        return False
    
    # Test RSI
    rsi = indicators.get('rsi')
    if rsi is not None and isinstance(rsi, pd.Series):
        non_nan_rsi = rsi.dropna()
        if len(non_nan_rsi) > 0:
            print(f"✅ RSI working: {len(non_nan_rsi)} values, latest: {non_nan_rsi.iloc[-1]:.2f}")
        else:
            print("⚠️  RSI has no non-NaN values")
    
    # Test ATR
    atr = indicators.get('atr')
    if atr is not None and isinstance(atr, pd.Series):
        non_nan_atr = atr.dropna()
        if len(non_nan_atr) > 0:
            print(f"✅ ATR working: {len(non_nan_atr)} values, latest: {non_nan_atr.iloc[-1]:.2f}")
        else:
            print("⚠️  ATR has no non-NaN values")
    
    # Test AVGPRICE
    avg_price = indicators.get('avg_price')
    if avg_price is not None and isinstance(avg_price, pd.Series):
        non_nan_avg = avg_price.dropna()
        if len(non_nan_avg) > 0:
            print(f"✅ AVGPRICE working: {len(non_nan_avg)} values, latest: {non_nan_avg.iloc[-1]:.2f}")
        else:
            print("⚠️  AVGPRICE has no non-NaN values")
    
    return True

if __name__ == "__main__":
    print("🚀 Running BBANDS fix verification tests...")
    print("=" * 50)
    
    success = test_bbands_fix()
    if success:
        test_other_indicators()
        print("\n" + "=" * 50)
        print("🎉 All tests passed! BBANDS fix is working correctly.")
    else:
        print("\n" + "=" * 50)
        print("❌ Tests failed. Please check the implementation.")
        sys.exit(1)
