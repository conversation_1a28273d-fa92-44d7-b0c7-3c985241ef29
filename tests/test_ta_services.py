import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from services.ta_services import TAService
from services.binance_client import BinanceClient
from utils.load_config import load_config

config = load_config("config/config.yaml")
binance_client = BinanceClient(config)
ta_service = TAService(binance_client)
indicators = ta_service.get_indicatos_value()
print(indicators)